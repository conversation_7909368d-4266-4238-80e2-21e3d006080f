'use client';

import type { AuthChangeEvent, Session } from '@supabase/supabase-js';
import { useCallback, useEffect, useState } from 'react';

import { notificationService } from '@/lib/services/notification-service';
import { userStore } from '@/lib/store/user';
import { supabaseClient } from '@/lib/supabase/auth/client';
import {
  createStandardError,
  logError,
  ERROR_MESSAGES,
} from '@/lib/utils/error-utils';
import type {
  CreateIssueInput,
  FilterOptions,
  Issue,
  IssuesState,
} from '@/lib/supabase/database-modules';

const supabase = supabaseClient;

export function useProfile() {
  const {
    profile,
    user,
    removeProfile,
    removeUser,
    updateProfile,
    updateUser,
  } = userStore();

  const [userId, setUserId] = useState<string>(user?.id ?? '');

  // Memoize the session fetch and update logic
  const fetchSessionAndUpdateUser = useCallback(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (session) {
        updateUser(session.user);
        setUserId(session.user.id);
      }
    });
  }, [updateUser]);

  // Memoize the auth state change handler
  const handleAuthStateChange = useCallback(
    (_event: AuthChangeEvent, session: Session | null) => {
      if (session) {
        updateUser(session.user);
        setUserId(session.user.id); // Optionally update userId here as well
      } else {
        removeUser(null);
        removeProfile(null);
      }
    },
    [updateUser, removeUser, removeProfile]
  );

  // Memoize the profile fetch logic
  const fetchProfile = useCallback(() => {
    if (userId) {
      supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()
        .then(({ data }) => {
          updateProfile(data);
        });
    }
  }, [userId, updateProfile]);

  // Effect for initial session fetch
  useEffect(() => {
    fetchSessionAndUpdateUser();
  }, [fetchSessionAndUpdateUser]);

  // Effect for auth state changes
  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(
      handleAuthStateChange
    );

    // Cleanup subscription on unmount
    return () => {
      authListener?.subscription?.unsubscribe();
    };
  }, [handleAuthStateChange]);

  // Effect for fetching profile
  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  return {
    profile,
  };
}

// ============================================================================
// Issues Management Hook
// ============================================================================

export function useIssues() {
  const [state, setState] = useState<IssuesState>({
    issues: [],
    issuesByStatus: {},
    loading: true,
    error: null,
  });

  const [realtimeUnsubscribe, setRealtimeUnsubscribe] = useState<
    (() => void) | null
  >(null);

  // Helper function to group issues by status
  const groupIssuesByStatus = useCallback(
    (issues: Issue[]): Record<string, Issue[]> => {
      return issues.reduce(
        (acc, issue) => {
          const statusId = issue.status_id;
          if (!acc[statusId]) {
            acc[statusId] = [];
          }
          acc[statusId].push(issue);
          return acc;
        },
        {} as Record<string, Issue[]>
      );
    },
    []
  );

  // Helper function to build the complete query with joins
  const buildIssueQuery = useCallback(() => {
    return supabase.from('issues').select(`
      *,
      status:status_id (
        id,
        name,
        color,
        icon_name,
        sort_order
      ),
      assignee:assignee_id (
        id,
        name,
        email,
        avatar_url,
        status,
        role,
        joined_date
      ),
      priority:priority_id (
        id,
        name,
        icon_name,
        sort_order
      ),
      project:project_id (
        id,
        name,
        description,
        icon,
        percent_complete,
        start_date,
        target_date,
        lead_id,
        status_id,
        priority_id,
        health_id
      ),
      cycle:cycle_id (
        id,
        number,
        name,
        team_id,
        start_date,
        end_date,
        progress
      ),
      issue_labels (
        label:label_id (
          id,
          name,
          color
        )
      )
    `);
  }, []);

  // Transform the data to match the expected format
  const transformIssueData = useCallback((data: unknown[]): Issue[] => {
    return data.map((item: unknown) => {
      const issueItem = item as Record<string, unknown>;
      const transformedIssue = {
        ...issueItem,
        status: issueItem.status,
        assignee: issueItem.assignee,
        priority: issueItem.priority,
        project: issueItem.project,
        cycle: issueItem.cycle,
        labels: Array.isArray(issueItem.issue_labels)
          ? issueItem.issue_labels.map(
              (il: unknown) => (il as Record<string, unknown>).label
            )
          : [],
        subissues: [], // TODO: Implement subissues query if needed
      };
      return transformedIssue as unknown as Issue;
    });
  }, []);

  // Data Fetching Functions (return data directly)
  const getAllIssues = useCallback((): Issue[] => {
    return state.issues;
  }, [state.issues]);

  const getIssueById = useCallback(
    (id: string): Issue | undefined => {
      return state.issues.find((issue) => issue.id === id);
    },
    [state.issues]
  );

  const getIssuesByStatus = useCallback(
    (statusId: string): Issue[] => {
      return state.issues.filter((issue) => issue.status_id === statusId);
    },
    [state.issues]
  );

  const searchIssues = useCallback(
    (query: string): Issue[] => {
      if (!query.trim()) return [];

      const lowercaseQuery = query.toLowerCase();
      return state.issues.filter(
        (issue) =>
          issue.title.toLowerCase().includes(lowercaseQuery) ||
          issue.description.toLowerCase().includes(lowercaseQuery) ||
          issue.identifier.toLowerCase().includes(lowercaseQuery)
      );
    },
    [state.issues]
  );

  const filterIssues = useCallback(
    (filters: FilterOptions): Issue[] => {
      let filteredIssues = state.issues;

      // Filter by status
      if (filters.status && filters.status.length > 0) {
        filteredIssues = filteredIssues.filter(
          (issue) => filters.status?.includes(issue.status_id) ?? false
        );
      }

      // Filter by assignee
      if (filters.assignee && filters.assignee.length > 0) {
        filteredIssues = filteredIssues.filter((issue) => {
          if (filters.assignee?.includes('unassigned')) {
            if (issue.assignee_id === null) {
              return true;
            }
          }
          return (
            issue.assignee_id &&
            (filters.assignee?.includes(issue.assignee_id) ?? false)
          );
        });
      }

      // Filter by priority
      if (filters.priority && filters.priority.length > 0) {
        filteredIssues = filteredIssues.filter(
          (issue) => filters.priority?.includes(issue.priority_id) ?? false
        );
      }

      // Filter by labels
      if (filters.labels && filters.labels.length > 0) {
        filteredIssues = filteredIssues.filter(
          (issue) =>
            issue.labels?.some(
              (label) => filters.labels?.includes(label.id) ?? false
            ) ?? false
        );
      }

      // Filter by project
      if (filters.project && filters.project.length > 0) {
        filteredIssues = filteredIssues.filter(
          (issue) =>
            issue.project_id &&
            (filters.project?.includes(issue.project_id) ?? false)
        );
      }

      return filteredIssues;
    },
    [state.issues]
  );

  // Fetch all issues from database
  const fetchIssues = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      const { data, error } = await buildIssueQuery().order('rank', {
        ascending: false,
      });

      if (error) throw error;

      const transformedIssues = transformIssueData(data || []);
      const groupedIssues = groupIssuesByStatus(transformedIssues);

      setState((prev) => ({
        ...prev,
        issues: transformedIssues,
        issuesByStatus: groupedIssues,
        loading: false,
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error:
          error instanceof Error ? error.message : 'Failed to fetch issues',
      }));
    }
  }, [buildIssueQuery, transformIssueData, groupIssuesByStatus]);

  // Action Functions (return Promise with resolve/reject pattern)
  const addIssue = useCallback(
    (issueInput: CreateIssueInput): Promise<Issue> => {
      return new Promise((resolve, reject) => {
        // Extract labels from issueInput to avoid inserting into issues table
        const { labels, ...issueDataWithoutLabels } = issueInput;

        // Get current user
        supabase.auth
          .getUser()
          .then(({ data: { user }, error: authError }) => {
            if (authError || !user) {
              logError(
                'addIssue - Authentication',
                authError || 'No user found'
              );
              reject(
                createStandardError(authError, ERROR_MESSAGES.NOT_AUTHENTICATED)
              );
              return;
            }

            // Generate unique identifier
            const identifier = `ISSUE-${Date.now()}`;

            const newIssue = {
              ...issueDataWithoutLabels,
              identifier,
              created_by: user.id,
            };

            // Insert the issue
            return supabase.from('issues').insert([newIssue]).select().single();
          })
          .then((result) => {
            if (!result) {
              reject(new Error('No result from insert operation'));
              return;
            }

            const { data, error } = result;
            if (error) {
              logError('addIssue - Database Insert', error, { issueInput });
              reject(
                createStandardError(error, 'Failed to create issue in database')
              );
              return;
            }

            // Add labels if provided
            if (labels && labels.length > 0) {
              const labelInserts = labels.map((labelId) => ({
                issue_id: data.id,
                label_id: labelId,
              }));

              return supabase
                .from('issue_labels')
                .insert(labelInserts)
                .then((labelResult) => {
                  if (labelResult.error) {
                    logError('addIssue - Label Insert', labelResult.error, {
                      issueId: data.id,
                      labelIds: labels,
                    });
                    throw createStandardError(
                      labelResult.error,
                      'Failed to add labels to issue'
                    );
                  }
                  return data; // Return the issue data after labels are inserted
                });
            }

            return Promise.resolve(data);
          })
          .then((issueData) => {
            if (!issueData) {
              reject(new Error('No issue data returned'));
              return;
            }

            // Create a basic issue object without complex relationships
            // to avoid schema cache issues. The realtime subscription will
            // handle updating with complete data including labels.
            const basicIssue: Issue = {
              id: issueData.id,
              identifier: issueData.identifier,
              title: issueData.title,
              description: issueData.description || '',
              status_id: issueData.status_id,
              assignee_id: issueData.assignee_id,
              priority_id: issueData.priority_id,
              project_id: issueData.project_id,
              cycle_id: issueData.cycle_id,
              parent_issue_id: issueData.parent_issue_id,
              rank: issueData.rank,
              due_date: issueData.due_date,
              created_at: issueData.created_at ?? '',
              updated_at: issueData.updated_at ?? '',
              created_by: issueData.created_by,
              // Basic labels array - will be updated by realtime
              labels: labels
                ? labels.map((labelId) => ({
                    id: labelId,
                    name: '',
                    color: '',
                  }))
                : [],
            };

            // Optimistic update with basic issue data
            setState((prev) => {
              const newIssues = [...prev.issues, basicIssue];
              return {
                ...prev,
                issues: newIssues,
                issuesByStatus: groupIssuesByStatus(newIssues),
              };
            });

            resolve(basicIssue);
          })
          .catch((error) => {
            logError('addIssue - Promise Chain', error, { issueInput });
            reject(createStandardError(error, ERROR_MESSAGES.CREATE_FAILED));
          });
      });
    },
    [groupIssuesByStatus]
  );

  const updateIssue = useCallback(
    (id: string, updates: Partial<Issue>): Promise<Issue> => {
      return new Promise((resolve, reject) => {
        supabase
          .from('issues')
          .update(updates)
          .eq('id', id)
          .select()
          .single()
          .then((result) => {
            try {
              if (!result) {
                reject(new Error('No result from update operation'));
                return;
              }

              const { error } = result;
              if (error) {
                reject(error);
                return;
              }

              // Fetch the complete updated issue data
              buildIssueQuery()
                .eq('id', id)
                .single()
                .then((fetchResult) => {
                  try {
                    if (!fetchResult) {
                      reject(new Error('No result from fetch operation'));
                      return;
                    }

                    const { data: completeIssue, error: fetchError } =
                      fetchResult;
                    if (fetchError) {
                      reject(fetchError);
                      return;
                    }

                    const transformedIssue = transformIssueData([
                      completeIssue,
                    ])[0];

                    // Optimistic update
                    setState((prev) => {
                      const newIssues = prev.issues.map((issue) =>
                        issue.id === id ? transformedIssue : issue
                      );
                      return {
                        ...prev,
                        issues: newIssues,
                        issuesByStatus: groupIssuesByStatus(newIssues),
                      };
                    });

                    resolve(transformedIssue);
                  } catch (fetchError) {
                    reject(
                      fetchError instanceof Error
                        ? fetchError
                        : new Error('Failed to fetch updated issue')
                    );
                  }
                });
            } catch (error) {
              reject(
                error instanceof Error
                  ? error
                  : new Error('Failed to update issue')
              );
            }
          });
      });
    },
    [buildIssueQuery, transformIssueData, groupIssuesByStatus]
  );

  const deleteIssue = useCallback(
    (id: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        supabase
          .from('issues')
          .delete()
          .eq('id', id)
          .then((result) => {
            try {
              if (!result) {
                reject(new Error('No result from delete operation'));
                return;
              }

              const { error } = result;
              if (error) {
                reject(error);
                return;
              }

              // Optimistic update
              setState((prev) => {
                const newIssues = prev.issues.filter(
                  (issue) => issue.id !== id
                );
                return {
                  ...prev,
                  issues: newIssues,
                  issuesByStatus: groupIssuesByStatus(newIssues),
                };
              });

              resolve();
            } catch (error) {
              reject(
                error instanceof Error
                  ? error
                  : new Error('Failed to delete issue')
              );
            }
          });
      });
    },
    [groupIssuesByStatus]
  );

  // Real-time subscriptions setup
  useEffect(() => {
    fetchIssues();

    try {
      // Set up real-time subscriptions using direct supabase client
      const issuesChannel = supabase
        .channel('issues-changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'issues' },
          (payload) => {
            try {
              console.log('Issues real-time change:', payload);

              // Show notifications for different events
              if (payload.eventType === 'INSERT' && payload.new) {
                const issue = payload.new as Issue;
                notificationService.issueCreated(issue.title);
              } else if (
                payload.eventType === 'UPDATE' &&
                payload.new &&
                payload.old
              ) {
                const newIssue = payload.new as Issue;
                const oldIssue = payload.old as Issue;

                if (newIssue.status_id !== oldIssue.status_id) {
                  // Status changed - we'd need to fetch status name, for now just show generic message
                  notificationService.issueUpdated(newIssue.title);
                } else if (newIssue.assignee_id !== oldIssue.assignee_id) {
                  notificationService.issueUpdated(newIssue.title);
                } else {
                  notificationService.issueUpdated(newIssue.title);
                }
              } else if (payload.eventType === 'DELETE' && payload.old) {
                const issue = payload.old as Issue;
                notificationService.issueDeleted(issue.title);
              }

              // Refetch issues to get updated data
              fetchIssues();
            } catch (error) {
              console.error('Error handling issues realtime event:', error);
              // Still try to refetch issues as fallback
              fetchIssues();
            }
          }
        )
        .subscribe();

      const issueLabelsChannel = supabase
        .channel('issue-labels-changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'issue_labels' },
          (payload) => {
            try {
              console.log('Issue labels real-time change:', payload);
              // Refetch issues when label associations change
              fetchIssues();
            } catch (error) {
              console.error(
                'Error handling issue labels realtime event:',
                error
              );
              // Still try to refetch issues as fallback
              fetchIssues();
            }
          }
        )
        .subscribe();

      // Combine unsubscribe functions
      const combinedUnsubscribe = () => {
        try {
          supabase.removeChannel(issuesChannel);
          supabase.removeChannel(issueLabelsChannel);
        } catch (error) {
          console.error('Error unsubscribing from realtime:', error);
        }
      };

      setRealtimeUnsubscribe(() => combinedUnsubscribe);

      return combinedUnsubscribe;
    } catch (error) {
      console.error('Error setting up realtime subscriptions:', error);
      // Return a no-op function if subscription setup fails
      return () => {};
    }
  }, [fetchIssues]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (realtimeUnsubscribe) {
        realtimeUnsubscribe();
      }
    };
  }, [realtimeUnsubscribe]);

  return {
    // State
    issues: state.issues,
    issuesByStatus: state.issuesByStatus,
    loading: state.loading,
    error: state.error,

    // Data fetching functions (return data directly)
    getAllIssues,
    getIssueById,
    getIssuesByStatus,
    searchIssues,
    filterIssues,

    // Action functions (return Promise with resolve/reject pattern)
    addIssue,
    updateIssue,
    deleteIssue,

    // Utility functions
    fetchIssues,
  };
}

interface Label {
  id: string;
  name: string;
  color: string;
  created_at: string | null;
}

// Hook for managing labels
export function useLabels() {
  const [labels, setLabels] = useState<Label[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLabels = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabaseClient
        .from('labels')
        .select('*')
        .order('name');

      if (fetchError) {
        throw fetchError;
      }

      setLabels(data || []);
    } catch (err) {
      console.error('Error fetching labels:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch labels');
    } finally {
      setLoading(false);
    }
  }, []);

  // Set up real-time subscription
  useEffect(() => {
    fetchLabels();

    try {
      const labelsChannel = supabase
        .channel('labels-changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'labels' },
          (payload) => {
            try {
              console.log('Labels real-time change:', payload);
              fetchLabels();
            } catch (error) {
              console.error('Error handling labels realtime event:', error);
              // Still try to refetch labels as fallback
              fetchLabels();
            }
          }
        )
        .subscribe();

      return () => {
        try {
          supabase.removeChannel(labelsChannel);
        } catch (error) {
          console.error('Error unsubscribing from labels realtime:', error);
        }
      };
    } catch (error) {
      console.error('Error setting up labels realtime subscription:', error);
      // Return a no-op function if subscription setup fails
      return () => {};
    }
  }, [fetchLabels]);

  return {
    labels,
    loading,
    error,
    refetch: fetchLabels,
  };
}

interface Priority {
  id: string;
  name: string;
  icon_name: string | null;
  sort_order: number | null;
  created_at: string | null;
}

// Hook for managing priorities
export function usePriorities() {
  const [priorities, setPriorities] = useState<Priority[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPriorities = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabaseClient
        .from('priorities')
        .select('*')
        .order('sort_order', { ascending: true });

      if (fetchError) {
        throw fetchError;
      }

      setPriorities(data || []);
    } catch (err) {
      console.error('Error fetching priorities:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to fetch priorities'
      );
    } finally {
      setLoading(false);
    }
  }, []);

  // Set up real-time subscription
  useEffect(() => {
    fetchPriorities();

    try {
      const prioritiesChannel = supabase
        .channel('priorities-changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'priorities' },
          (payload) => {
            try {
              console.log('Priorities real-time change:', payload);
              fetchPriorities();
            } catch (error) {
              console.error('Error handling priorities realtime event:', error);
              // Still try to refetch priorities as fallback
              fetchPriorities();
            }
          }
        )
        .subscribe();

      return () => {
        try {
          supabase.removeChannel(prioritiesChannel);
        } catch (error) {
          console.error('Error unsubscribing from priorities realtime:', error);
        }
      };
    } catch (error) {
      console.error(
        'Error setting up priorities realtime subscription:',
        error
      );
      // Return a no-op function if subscription setup fails
      return () => {};
    }
  }, [fetchPriorities]);

  return {
    priorities,
    loading,
    error,
    refetch: fetchPriorities,
  };
}

interface Project {
  id: string;
  name: string;
  description: string | null;
  status: string | null;
  priority: string | null;
  start_date: string | null;
  due_date: string | null;
  completed_date: string | null;
  client_name: string | null;
  budget: number | null;
  created_at: string | null;
  updated_at: string | null;
  created_by: string | null;
  is_archived: boolean;
  icon: string | null;
  percent_complete: number | null;
  target_date: string | null;
  lead_id: string | null;
  status_id: string | null;
  priority_id: string | null;
  health_id: string | null;
}

// Hook for managing projects
export function useProjects() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Check authentication status
      const {
        data: { session },
      } = await supabaseClient.auth.getSession();
      console.log(
        'Projects fetch - Auth session:',
        session ? 'authenticated' : 'not authenticated'
      );

      const { data, error: fetchError } = await supabaseClient
        .from('projects')
        .select(
          `
          id,
          name,
          description,
          status,
          priority,
          start_date,
          due_date,
          completed_date,
          client_name,
          budget,
          created_at,
          updated_at,
          created_by,
          is_archived,
          icon,
          percent_complete,
          target_date,
          lead_id,
          status_id,
          priority_id,
          health_id
        `
        )
        .order('name');

      if (fetchError) {
        console.error('Supabase error fetching projects:', {
          message: fetchError.message,
          details: fetchError.details,
          hint: fetchError.hint,
          code: fetchError.code,
        });
        throw fetchError;
      }

      console.log(
        'Projects fetched successfully:',
        data?.length || 0,
        'projects'
      );
      setProjects(data || []);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to fetch projects';
      const errorDetails = {
        message: errorMessage,
        error: err,
        stack: err instanceof Error ? err.stack : undefined,
      };

      console.error('Error fetching projects:', errorDetails);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Set up real-time subscription
  useEffect(() => {
    fetchProjects();

    try {
      const projectsChannel = supabase
        .channel('projects-changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'projects' },
          (payload) => {
            try {
              console.log('Projects real-time change:', payload);
              fetchProjects();
            } catch (error) {
              console.error('Error handling projects realtime event:', error);
              // Still try to refetch projects as fallback
              fetchProjects();
            }
          }
        )
        .subscribe();

      return () => {
        try {
          supabase.removeChannel(projectsChannel);
        } catch (error) {
          console.error('Error unsubscribing from projects realtime:', error);
        }
      };
    } catch (error) {
      console.error('Error setting up projects realtime subscription:', error);
      // Return a no-op function if subscription setup fails
      return () => {};
    }
  }, [fetchProjects]);

  return {
    projects,
    loading,
    error,
    refetch: fetchProjects,
  };
}

interface Status {
  id: string;
  name: string;
  color: string;
  sort_order: number | null;
  created_at: string | null;
}

// Hook for managing status options
export function useStatus() {
  const [status, setStatus] = useState<Status[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabaseClient
        .from('status')
        .select('*')
        .order('sort_order', { ascending: true });

      if (fetchError) {
        throw fetchError;
      }

      setStatus(data || []);
    } catch (err) {
      console.error('Error fetching status:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch status');
    } finally {
      setLoading(false);
    }
  }, []);

  // Set up real-time subscription
  useEffect(() => {
    fetchStatus();

    try {
      const statusChannel = supabase
        .channel('status-changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'status' },
          (payload) => {
            try {
              console.log('Status real-time change:', payload);
              fetchStatus();
            } catch (error) {
              console.error('Error handling status realtime event:', error);
              // Still try to refetch status as fallback
              fetchStatus();
            }
          }
        )
        .subscribe();

      return () => {
        try {
          supabase.removeChannel(statusChannel);
        } catch (error) {
          console.error('Error unsubscribing from status realtime:', error);
        }
      };
    } catch (error) {
      console.error('Error setting up status realtime subscription:', error);
      // Return a no-op function if subscription setup fails
      return () => {};
    }
  }, [fetchStatus]);

  return {
    status,
    loading,
    error,
    refetch: fetchStatus,
  };
}
